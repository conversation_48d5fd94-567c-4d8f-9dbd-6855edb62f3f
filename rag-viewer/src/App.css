* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: #f5f5f5;
}

.upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
}

.upload-area {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  margin: 1rem 0;
}

.upload-area input[type="file"] {
  display: none;
}

.upload-area label {
  cursor: pointer;
  padding: 1rem 2rem;
  background: #007bff;
  color: white;
  border-radius: 4px;
  display: inline-block;
}

.upload-area label:hover {
  background: #0056b3;
}

.error {
  color: #dc3545;
  margin-top: 1rem;
}

.app {
  max-width: 100vw;
  margin: 0;
  background: white;
  min-height: 100vh;
}

header {
  background: #f8f9fa;
  padding: 1rem 2rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

header h1 {
  margin: 0;
  color: #333;
}

.metadata {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.metadata span {
  background: #e9ecef;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
}

.reset-btn {
  padding: 0.5rem 1rem;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.reset-btn:hover {
  background: #545b62;
}

.content {
  display: flex;
  height: calc(100vh - 80px);
}

.sessions-list {
  width: 400px;
  border-right: 1px solid #dee2e6;
  overflow-y: auto;
  padding: 1rem;
}

.sessions-list h2 {
  margin-top: 0;
  color: #333;
}

.session-item {
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 1rem;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
}

.session-item:hover {
  background: #f8f9fa;
}

.session-item.selected {
  background: #e3f2fd;
  border-color: #2196f3;
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.session-time {
  font-size: 0.875rem;
  color: #666;
}

.session-chunks {
  background: #e9ecef;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
}

.session-summary {
  font-size: 0.875rem;
  color: #333;
}

.session-details {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.details-container h2 {
  margin-top: 0;
  color: #333;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #dee2e6;
  margin-bottom: 1rem;
}

.tabs button {
  padding: 0.75rem 1rem;
  border: none;
  background: none;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.tabs button.active {
  border-bottom-color: #007bff;
  color: #007bff;
}

.tabs button:hover {
  background: #f8f9fa;
}

.question-item {
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.question-item h3 {
  margin-top: 0;
  color: #333;
}

.question-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.question-meta span {
  background: #e9ecef;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
}

.question-text, .answer-text, .answer-context, .answer-reason {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.raw-output pre {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  white-space: pre-wrap;
}

.chunk-item {
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.chunk-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.chunk-header h3 {
  margin: 0;
  color: #333;
}

.chunk-meta {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.chunk-meta span {
  background: #e9ecef;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
}

.chunk-content pre {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  white-space: pre-wrap;
  margin: 0;
}

.query-item {
  margin-bottom: 2rem;
}

.query-item h3 {
  color: #333;
  margin-bottom: 0.5rem;
}

.query-item pre {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  white-space: pre-wrap;
  margin: 0;
}
