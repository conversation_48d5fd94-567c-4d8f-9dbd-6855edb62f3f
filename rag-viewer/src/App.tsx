import { useState } from 'react'
import './App.css'

interface RetrievalData {
  timestamp: number
  embedding_model: string
  client_id: string
  assessment_id: string
  retrieval_info: {
    retrieved_chunks: Array<{
      chunk_index: number
      content: string
      metadata: any
      question_codes: string[]
    }>
    query: string
    llm_input: string
    llm_output: string
  }
}

interface RAGData {
  embedding_model: string
  client_id: string
  assessment_id: string
  timestamp: number
  total_queries: number
  retrieval_data: RetrievalData[]
}

function App() {
  const [ragData, setRagData] = useState<RAGData | null>(null)
  const [selectedSession, setSelectedSession] = useState<number | null>(null)
  const [error, setError] = useState<string>('')

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const jsonData = JSON.parse(e.target?.result as string)
        setRagData(jsonData)
        setError('')
      } catch (err) {
        setError('Invalid JSON file')
      }
    }
    reader.readAsText(file)
  }

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString()
  }

  const parseQuestions = (llmOutput: string) => {
    try {
      // Try to parse as JSON array first
      const parsed = JSON.parse(llmOutput)
      if (Array.isArray(parsed)) {
        return parsed
      }
      // If it's a single object, wrap in array
      return [parsed]
    } catch {
      // If parsing fails, return raw output
      return [{ raw_output: llmOutput }]
    }
  }

  if (!ragData) {
    return (
      <div className="upload-container">
        <h1>RAG Performance Viewer</h1>
        <div className="upload-area">
          <input
            type="file"
            accept=".json"
            onChange={handleFileUpload}
            id="file-upload"
          />
          <label htmlFor="file-upload">
            Choose JSON file or drag and drop
          </label>
        </div>
        {error && <div className="error">{error}</div>}
      </div>
    )
  }

  return (
    <div className="app">
      <header>
        <h1>RAG Performance Analysis</h1>
        <div className="metadata">
          <span>Client: {ragData.client_id}</span>
          <span>Assessment: {ragData.assessment_id}</span>
          <span>Total Queries: {ragData.total_queries}</span>
          <span>Model: {ragData.embedding_model}</span>
        </div>
        <button onClick={() => setRagData(null)} className="reset-btn">
          Upload New File
        </button>
      </header>

      <div className="content">
        <div className="sessions-list">
          <h2>LLM Processing Sessions ({ragData.retrieval_data.length})</h2>
          {ragData.retrieval_data.map((session, index) => {
            const questions = parseQuestions(session.retrieval_info.llm_output)
            return (
              <div
                key={index}
                className={`session-item ${selectedSession === index ? 'selected' : ''}`}
                onClick={() => setSelectedSession(index)}
              >
                <div className="session-header">
                  <span className="session-time">
                    {formatTimestamp(session.timestamp)}
                  </span>
                  <span className="session-chunks">
                    {session.retrieval_info.retrieved_chunks.length} chunks
                  </span>
                </div>
                <div className="session-summary">
                  {Array.isArray(questions) ? (
                    <span>{questions.length} questions answered</span>
                  ) : (
                    <span>Single response</span>
                  )}
                </div>
              </div>
            )
          })}
        </div>

        {selectedSession !== null && (
          <div className="session-details">
            <SessionDetails
              session={ragData.retrieval_data[selectedSession]}
              sessionIndex={selectedSession}
            />
          </div>
        )}
      </div>
    </div>
  )
}

interface SessionDetailsProps {
  session: RetrievalData
  sessionIndex: number
}

function SessionDetails({ session, sessionIndex }: SessionDetailsProps) {
  const [activeTab, setActiveTab] = useState<'questions' | 'chunks' | 'query'>('questions')

  const questions = (() => {
    try {
      const parsed = JSON.parse(session.retrieval_info.llm_output)
      return Array.isArray(parsed) ? parsed : [parsed]
    } catch {
      return [{ raw_output: session.retrieval_info.llm_output }]
    }
  })()

  return (
    <div className="details-container">
      <h2>Session {sessionIndex + 1} Details</h2>

      <div className="tabs">
        <button
          className={activeTab === 'questions' ? 'active' : ''}
          onClick={() => setActiveTab('questions')}
        >
          LLM Answers ({questions.length})
        </button>
        <button
          className={activeTab === 'chunks' ? 'active' : ''}
          onClick={() => setActiveTab('chunks')}
        >
          Retrieved Chunks ({session.retrieval_info.retrieved_chunks.length})
        </button>
        <button
          className={activeTab === 'query' ? 'active' : ''}
          onClick={() => setActiveTab('query')}
        >
          Query & Input
        </button>
      </div>

      <div className="tab-content">
        {activeTab === 'questions' && (
          <div className="questions-section">
            {questions.map((q, idx) => (
              <div key={idx} className="question-item">
                <h3>Question {idx + 1}</h3>
                {q.question_code && (
                  <div className="question-meta">
                    <span><strong>Code:</strong> {q.question_code}</span>
                    {q.question_type && <span><strong>Type:</strong> {q.question_type}</span>}
                  </div>
                )}
                {q.question_text && (
                  <div className="question-text">
                    <strong>Question:</strong> {q.question_text}
                  </div>
                )}
                {q.answer_text && (
                  <div className="answer-text">
                    <strong>Answer:</strong> {Array.isArray(q.answer_text) ? q.answer_text.join(', ') : q.answer_text}
                  </div>
                )}
                {q.answer_context && (
                  <div className="answer-context">
                    <strong>Context:</strong> {Array.isArray(q.answer_context) ? q.answer_context.join(', ') : q.answer_context}
                  </div>
                )}
                {q.answer_reason && (
                  <div className="answer-reason">
                    <strong>Reasoning:</strong> {Array.isArray(q.answer_reason) ? q.answer_reason.join(' ') : q.answer_reason}
                  </div>
                )}
                {q.raw_output && (
                  <div className="raw-output">
                    <strong>Raw Output:</strong>
                    <pre>{q.raw_output}</pre>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {activeTab === 'chunks' && (
          <div className="chunks-section">
            {session.retrieval_info.retrieved_chunks.map((chunk, idx) => (
              <div key={idx} className="chunk-item">
                <div className="chunk-header">
                  <h3>Chunk {chunk.chunk_index}</h3>
                  <div className="chunk-meta">
                    <span>Type: {chunk.metadata.chunk_type}</span>
                    <span>Timing: {chunk.metadata.relative_timing}</span>
                    {chunk.question_codes.length > 0 && (
                      <span>OASIS: {chunk.question_codes.join(', ')}</span>
                    )}
                  </div>
                </div>
                <div className="chunk-content">
                  <pre>{chunk.content}</pre>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'query' && (
          <div className="query-section">
            <div className="query-item">
              <h3>Original Query</h3>
              <pre>{session.retrieval_info.query}</pre>
            </div>
            <div className="query-item">
              <h3>LLM Input</h3>
              <pre>{session.retrieval_info.llm_input}</pre>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default App
