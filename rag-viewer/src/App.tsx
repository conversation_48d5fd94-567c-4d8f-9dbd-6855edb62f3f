import { useState } from 'react'
import './App.css'

interface RetrievalData {
  timestamp: number
  embedding_model: string
  client_id: string
  assessment_id: string
  retrieval_info: {
    retrieved_chunks: Array<{
      chunk_index: number
      content: string
      metadata: any
      question_codes: string[]
    }>
    query: string
    llm_input: string
    llm_output: string
  }
}

interface RAGData {
  embedding_model: string
  client_id: string
  assessment_id: string
  timestamp: number
  total_queries: number
  retrieval_data: RetrievalData[]
}

function App() {
  const [ragData, setRagData] = useState<RAGData | null>(null)
  const [selectedSession, setSelectedSession] = useState<number | null>(null)
  const [error, setError] = useState<string>('')

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const jsonData = JSON.parse(e.target?.result as string)
        setRagData(jsonData)
        setError('')
      } catch (err) {
        setError('Invalid JSON file')
      }
    }
    reader.readAsText(file)
  }

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString()
  }

  const parseQuestions = (llmOutput: string) => {
    try {
      let output = llmOutput
      // Remove markdown code blocks if present
      if (output.includes('```json')) {
        output = output.replace(/```json\s*/, '').replace(/```\s*$/, '')
      }
      // Try to parse as JSON array first
      const parsed = JSON.parse(output)
      if (Array.isArray(parsed)) {
        return parsed
      }
      // If it's a single object, wrap in array
      return [parsed]
    } catch {
      // If parsing fails, return raw output
      return [{ raw_output: llmOutput }]
    }
  }

  if (!ragData) {
    return (
      <div className="upload-container">
        <h1>RAG Performance Viewer</h1>
        <div className="upload-area">
          <input
            type="file"
            accept=".json"
            onChange={handleFileUpload}
            id="file-upload"
          />
          <label htmlFor="file-upload">
            Choose JSON file or drag and drop
          </label>
        </div>
        {error && <div className="error">{error}</div>}
      </div>
    )
  }

  return (
    <div className="app">
      <header>
        <h1>RAG Performance Analysis</h1>
        <div className="metadata">
          <span>Client: {ragData.client_id}</span>
          <span>Assessment: {ragData.assessment_id}</span>
          <span>Total Queries: {ragData.total_queries}</span>
          <span>Model: {ragData.embedding_model}</span>
        </div>
        <button onClick={() => setRagData(null)} className="reset-btn">
          Upload New File
        </button>
      </header>

      <div className="content">
        <div className="file-sidebar">
          <h2>Uploaded File</h2>
          <div className="file-info">
            <div className="file-name">RAG Performance Report</div>
            <div className="file-stats">
              <span>{ragData.retrieval_data.length} Queries</span>
              <span>{ragData.total_queries} Total</span>
            </div>
          </div>
        </div>

        <div className="queries-content">
          <h2>All Queries ({ragData.retrieval_data.length})</h2>
          {ragData.retrieval_data.map((session, index) => (
            <QuerySection
              key={index}
              session={session}
              queryIndex={index}
            />
          ))}
        </div>
      </div>
    </div>
  )
}

interface QuerySectionProps {
  session: RetrievalData
  queryIndex: number
}

function QuerySection({ session, queryIndex }: QuerySectionProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [activeTab, setActiveTab] = useState<'questions' | 'chunks' | 'query'>('questions')

  const questions = (() => {
    try {
      let output = session.retrieval_info.llm_output
      // Remove markdown code blocks if present
      if (output.includes('```json')) {
        output = output.replace(/```json\s*/, '').replace(/```\s*$/, '')
      }
      const parsed = JSON.parse(output)
      return Array.isArray(parsed) ? parsed : [parsed]
    } catch {
      return [{ raw_output: session.retrieval_info.llm_output }]
    }
  })()

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString()
  }

  const getQuestionCodes = () => {
    const codes = questions
      .map(q => q.question_code)
      .filter(code => code && code !== 'N/A')
    return codes.length > 0 ? codes.join(', ') : 'No codes'
  }

  return (
    <div className="query-section-container">
      <div className="query-header" onClick={() => setIsExpanded(!isExpanded)}>
        <div className="query-title">
          <h3>Query {queryIndex + 1} ({getQuestionCodes()})</h3>
          <span className="query-time">{formatTimestamp(session.timestamp)}</span>
        </div>
        <div className="query-stats">
          <span className="questions-count">{questions.length} Questions</span>
          <span className="chunks-count">{session.retrieval_info.retrieved_chunks.length} Chunks</span>
          <span className="expand-icon">{isExpanded ? '▼' : '▶'}</span>
        </div>
      </div>

      {isExpanded && (
        <div className="query-details">
          <div className="tabs">
            <button
              className={activeTab === 'questions' ? 'active' : ''}
              onClick={() => setActiveTab('questions')}
            >
              Questions & Answers ({questions.length})
            </button>
            <button
              className={activeTab === 'chunks' ? 'active' : ''}
              onClick={() => setActiveTab('chunks')}
            >
              Retrieved Chunks ({session.retrieval_info.retrieved_chunks.length})
            </button>
            <button
              className={activeTab === 'query' ? 'active' : ''}
              onClick={() => setActiveTab('query')}
            >
              Query & Input
            </button>
          </div>

          <div className="tab-content">
            {activeTab === 'questions' && (
              <div className="questions-section">
                {questions.map((q, idx) => (
                  <div key={idx} className="question-item">
                    <h4>Question {idx + 1}</h4>
                    {q.question_code && (
                      <div className="question-meta">
                        <span><strong>Code:</strong> {q.question_code}</span>
                        {q.question_type && <span><strong>Type:</strong> {q.question_type}</span>}
                      </div>
                    )}
                    {q.question_text && (
                      <div className="question-text">
                        <strong>Question:</strong> {q.question_text}
                      </div>
                    )}
                    {q.answer_text && (
                      <div className="answer-text">
                        <strong>Answer:</strong> {Array.isArray(q.answer_text) ? q.answer_text.join(', ') : q.answer_text}
                      </div>
                    )}
                    {q.answer_context && (
                      <div className="answer-context">
                        <strong>Context:</strong> {Array.isArray(q.answer_context) ? q.answer_context.join(', ') : q.answer_context}
                      </div>
                    )}
                    {q.answer_reason && (
                      <div className="answer-reason">
                        <strong>Reasoning:</strong> {Array.isArray(q.answer_reason) ? q.answer_reason.join(' ') : q.answer_reason}
                      </div>
                    )}
                    {q.raw_output && (
                      <div className="raw-output">
                        <strong>Raw Output:</strong>
                        <pre>{q.raw_output}</pre>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {activeTab === 'chunks' && (
              <div className="chunks-section">
                {session.retrieval_info.retrieved_chunks.map((chunk, idx) => (
                  <div key={idx} className="chunk-item">
                    <div className="chunk-header">
                      <h4>Chunk {chunk.chunk_index}</h4>
                      <div className="chunk-meta">
                        <span>Type: {chunk.metadata.chunk_type}</span>
                        <span>Timing: {chunk.metadata.relative_timing}</span>
                        {chunk.question_codes.length > 0 && (
                          <span>OASIS: {chunk.question_codes.join(', ')}</span>
                        )}
                      </div>
                    </div>
                    <div className="chunk-content">
                      <pre>{chunk.content}</pre>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {activeTab === 'query' && (
              <div className="query-section">
                <div className="query-item">
                  <h4>Original Query</h4>
                  <pre>{session.retrieval_info.query}</pre>
                </div>
                <div className="query-item">
                  <h4>LLM Input</h4>
                  <pre>{session.retrieval_info.llm_input}</pre>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default App
