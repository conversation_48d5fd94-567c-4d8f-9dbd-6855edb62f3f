import type { TSESLint } from '@typescript-eslint/utils';
export type Options = [
    {
        caseSensitive?: boolean;
        checkIntersections?: boolean;
        checkUnions?: boolean;
        groupOrder?: string[];
    }
];
export type MessageIds = 'notSorted' | 'notSortedNamed' | 'suggestFix';
declare const _default: TSESLint.RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, TSESLint.RuleListener>;
export default _default;
//# sourceMappingURL=sort-type-constituents.d.ts.map